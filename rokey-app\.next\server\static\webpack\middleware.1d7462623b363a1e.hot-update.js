"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('🔥 MIDDLEWARE: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks',\n        // Policy and informational pages\n        '/terms',\n        '/privacy',\n        '/cookies',\n        '/security',\n        '/about',\n        '/contact',\n        '/features',\n        '/docs',\n        '/blog',\n        '/routing-strategies'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        // Special case for root route - only match exactly\n        if (route === '/') {\n            return pathname === '/';\n        }\n        // For other routes, allow exact match or startsWith\n        return pathname === route || pathname.startsWith(route);\n    });\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier, user_status').eq('id', session.user.id).single();\n            let isNoProfileError = false;\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // Check if this is a \"no rows\" error (user has no profile) vs a real network error\n                if (profileError.code === 'PGRST116') {\n                    // This means no profile exists - treat as no profile case\n                    isNoProfileError = true;\n                } else {\n                    // Real network error - allow access to prevent app from being blocked\n                    return res;\n                }\n            }\n            // Check if user has pending status (cannot access protected routes)\n            if (profile && profile.user_status === 'pending') {\n                // User exists but has pending status - redirect to complete payment\n                const userPlan = session.user.user_metadata?.plan || profile.subscription_tier;\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('message', 'complete_payment');\n                redirectUrl.searchParams.set('plan', userPlan);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n            // If no profile exists (either !profile or PGRST116 error), check if this is a pending payment user\n            if (!profile || isNoProfileError) {\n                // Check if user has pending payment status in their metadata (legacy check)\n                const paymentStatus = session.user.user_metadata?.payment_status;\n                const userPlan = session.user.user_metadata?.plan;\n                if (paymentStatus === 'pending' && userPlan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(userPlan)) {\n                    // This is a user who signed up for a paid plan but hasn't completed payment\n                    // Redirect to pricing page for fresh signup process\n                    const redirectUrl = new URL('/pricing', req.url);\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n                // Only create free profiles for users who don't have pending payments\n                console.log('Middleware: Creating default free profile for user:', session.user.id);\n                try {\n                    const { error: createError } = await supabase.from('user_profiles').insert({\n                        id: session.user.id,\n                        full_name: session.user.user_metadata?.full_name || '',\n                        subscription_tier: 'free',\n                        subscription_status: 'active',\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    if (createError) {\n                        console.error('Middleware: Error creating user profile:', createError);\n                        // If we can't create profile, redirect to pricing to be safe\n                        const redirectUrl = new URL('/pricing', req.url);\n                        redirectUrl.searchParams.set('checkout', 'true');\n                        redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                    }\n                    // Profile created successfully, allow access\n                    console.log('Middleware: Successfully created free profile for user:', session.user.id);\n                    return res;\n                } catch (error) {\n                    console.error('Middleware: Exception creating user profile:', error);\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('checkout', 'true');\n                    redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});