/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/check-pending-payment/route";
exports.ids = ["app/api/auth/check-pending-payment/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/check-pending-payment/route.ts */ \"(rsc)/./src/app/api/auth/check-pending-payment/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/check-pending-payment/route\",\n        pathname: \"/api/auth/check-pending-payment\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/check-pending-payment/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\check-pending-payment\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/check-pending-payment/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/auth/check-pending-payment/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/service-role */ \"(rsc)/./src/lib/supabase/service-role.ts\");\n\n\nasync function POST(req) {\n    console.log('🔥 CHECK-PENDING-PAYMENT: API called');\n    try {\n        const { email } = await req.json();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Email received:', email);\n        if (!email) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: No email provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__.createServiceRoleClient)();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Supabase client created');\n        // Check if user exists by looking up their profile (which contains the email info)\n        console.log('🔥 CHECK-PENDING-PAYMENT: Looking up user by email...');\n        // First, try to find user in auth.users table using raw SQL\n        const { data: authUsers, error: authError } = await supabase.rpc('get_user_by_email', {\n            user_email: email\n        });\n        if (authError) {\n            console.error('🔥 CHECK-PENDING-PAYMENT: Error with RPC call:', authError);\n            // Fallback: Check user_profiles table for existing users\n            console.log('🔥 CHECK-PENDING-PAYMENT: Fallback - checking user_profiles...');\n            const { data: profiles, error: profileError } = await supabase.from('user_profiles').select('id, subscription_tier, subscription_status, user_status').limit(100); // Get recent users to search through\n            if (profileError) {\n                console.error('🔥 CHECK-PENDING-PAYMENT: Error querying profiles:', profileError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to check user status'\n                }, {\n                    status: 500\n                });\n            }\n            // We can't easily match by email in profiles, so return not found\n            console.log('🔥 CHECK-PENDING-PAYMENT: Cannot verify user existence, assuming new user');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: false,\n                message: 'No account found with this email address.'\n            });\n        }\n        if (!authUsers || authUsers.length === 0) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: No user found with email:', email);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: false,\n                message: 'No account found with this email address.'\n            });\n        }\n        const user = authUsers[0];\n        console.log('🔥 CHECK-PENDING-PAYMENT: Found user:', user.id);\n        // Check user profile for status\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status, user_status').eq('id', user.id).single();\n        console.log('🔥 CHECK-PENDING-PAYMENT: User profile:', profile);\n        if (profile) {\n            // Check if user has pending status\n            if (profile.user_status === 'pending') {\n                console.log('🔥 CHECK-PENDING-PAYMENT: User has pending status');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: true,\n                    plan: profile.subscription_tier,\n                    userId: user.id,\n                    message: `You have a pending payment for your ${profile.subscription_tier} plan. Please complete your checkout.`,\n                    signInUrl: `/auth/signin?message=complete_payment&plan=${profile.subscription_tier}&email=${encodeURIComponent(email)}`\n                });\n            }\n            // Check if user has active subscription\n            if (profile.user_status === 'active' && profile.subscription_status === 'active') {\n                console.log('🔥 CHECK-PENDING-PAYMENT: User has active subscription');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: false,\n                    hasActiveSubscription: true,\n                    tier: profile.subscription_tier,\n                    message: `You already have an active ${profile.subscription_tier} account. Please sign in to access your dashboard.`,\n                    signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n                });\n            }\n        }\n        // Fallback: Check legacy user metadata for pending payment status\n        const paymentStatus = user.raw_user_meta_data?.payment_status;\n        const userPlan = user.raw_user_meta_data?.plan;\n        console.log('🔥 CHECK-PENDING-PAYMENT: Checking legacy metadata - payment:', paymentStatus, 'plan:', userPlan);\n        if (paymentStatus === 'pending' && userPlan && [\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(userPlan)) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: User has pending payment in metadata');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: true,\n                hasPendingPayment: true,\n                plan: userPlan,\n                userId: user.id,\n                message: `You have a pending payment for your ${userPlan} plan. Please complete your checkout.`,\n                signInUrl: `/auth/signin?message=complete_payment&plan=${userPlan}&email=${encodeURIComponent(email)}`\n            });\n        }\n        // User exists but no clear status\n        console.log('🔥 CHECK-PENDING-PAYMENT: User exists but no clear status');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            exists: true,\n            hasPendingPayment: false,\n            hasActiveSubscription: false,\n            message: 'Account found. Please sign in to continue.',\n            signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n        });\n    } catch (error) {\n        console.error('🔥 CHECK-PENDING-PAYMENT: Exception:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for testing\nasync function GET() {\n    console.log('🔥 CHECK-PENDING-PAYMENT: GET endpoint called');\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Pending payment check endpoint is working',\n        usage: 'POST /api/auth/check-pending-payment with { email: \"<EMAIL>\" }',\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/check-pending-payment/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/service-role.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/service-role.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Create service role client for server-side operations that need to bypass RLS\n * This client has full database access and should only be used for:\n * - Internal API operations\n * - Admin operations\n * - System-level operations\n * - External API key authentication\n */ function createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZpY2Utcm9sZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUVyRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0M7SUFDZCxPQUFPRCxzR0FBWUEsQ0FDakJFLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUIsRUFDckM7UUFDRUMsTUFBTTtZQUNKQyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2aWNlLXJvbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLyoqXG4gKiBDcmVhdGUgc2VydmljZSByb2xlIGNsaWVudCBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9ucyB0aGF0IG5lZWQgdG8gYnlwYXNzIFJMU1xuICogVGhpcyBjbGllbnQgaGFzIGZ1bGwgZGF0YWJhc2UgYWNjZXNzIGFuZCBzaG91bGQgb25seSBiZSB1c2VkIGZvcjpcbiAqIC0gSW50ZXJuYWwgQVBJIG9wZXJhdGlvbnNcbiAqIC0gQWRtaW4gb3BlcmF0aW9uc1xuICogLSBTeXN0ZW0tbGV2ZWwgb3BlcmF0aW9uc1xuICogLSBFeHRlcm5hbCBBUEkga2V5IGF1dGhlbnRpY2F0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/service-role.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();