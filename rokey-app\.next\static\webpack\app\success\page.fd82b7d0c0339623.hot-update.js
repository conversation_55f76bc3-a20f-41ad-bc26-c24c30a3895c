"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/success/page",{

/***/ "(app-pages-browser)/./src/app/success/page.tsx":
/*!**********************************!*\
  !*** ./src/app/success/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SuccessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SuccessPageContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { subscriptionStatus, refreshStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [planName, setPlanName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const confettiTriggered = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const sessionId = searchParams.get('session_id');\n    const plan = searchParams.get('plan') || (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier);\n    const manualSignIn = searchParams.get('manual_signin') === 'true';\n    const autoSignIn = searchParams.get('auto_signin') === 'true';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SuccessPageContent.useEffect\": ()=>{\n            const handleAutoSignIn = {\n                \"SuccessPageContent.useEffect.handleAutoSignIn\": async ()=>{\n                    // Handle auto sign-in for pending users\n                    if (autoSignIn && userId && email) {\n                        console.log('Auto sign-in requested for user:', userId, email);\n                        try {\n                            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createSupabaseBrowserClient)();\n                            // Check if user is already signed in\n                            const { data: { user } } = await supabase.auth.getUser();\n                            if (user && user.id === userId) {\n                                console.log('User already signed in, proceeding to dashboard');\n                                router.push('/dashboard?payment_success=true');\n                                return;\n                            }\n                            // Generate a magic link for auto sign-in\n                            console.log('Generating magic link for auto sign-in...');\n                            const response = await fetch('/api/auth/generate-magic-link', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    email: email,\n                                    userId: userId,\n                                    redirectTo: '/dashboard?payment_success=true'\n                                })\n                            });\n                            if (response.ok) {\n                                const data = await response.json();\n                                if (data.magicLink) {\n                                    console.log('Magic link generated, redirecting...');\n                                    window.location.href = data.magicLink;\n                                    return;\n                                }\n                            }\n                            console.log('Magic link generation failed, falling back to manual sign-in');\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        } catch (error) {\n                            console.error('Auto sign-in error:', error);\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        }\n                    }\n                    // Normal success page flow\n                    if (confettiTriggered.current) return;\n                    // Mark confetti as triggered to prevent infinite loop\n                    confettiTriggered.current = true;\n                    // Trigger confetti animation\n                    const triggerConfetti = {\n                        \"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\": ()=>{\n                            (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                                particleCount: 100,\n                                spread: 70,\n                                origin: {\n                                    y: 0.6\n                                }\n                            });\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\"];\n                    // Initial confetti\n                    setTimeout(triggerConfetti, 500);\n                    // Additional confetti bursts\n                    setTimeout(triggerConfetti, 1500);\n                    setTimeout(triggerConfetti, 2500);\n                    // Refresh subscription status\n                    refreshStatus();\n                    // Set plan name - only if we have a valid plan\n                    if (plan) {\n                        const planNames = {\n                            'starter': 'Starter',\n                            'professional': 'Professional',\n                            'enterprise': 'Enterprise'\n                        };\n                        setPlanName(planNames[plan] || plan.charAt(0).toUpperCase() + plan.slice(1));\n                    }\n                    // Show modal after a brief delay, but only if we have plan information\n                    setTimeout({\n                        \"SuccessPageContent.useEffect.handleAutoSignIn\": ()=>{\n                            setIsLoading(false);\n                            if (plan) {\n                                setShowModal(true);\n                            }\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn\"], 1000);\n                }\n            }[\"SuccessPageContent.useEffect.handleAutoSignIn\"];\n            handleAutoSignIn();\n        }\n    }[\"SuccessPageContent.useEffect\"], [\n        plan,\n        autoSignIn,\n        userId,\n        email,\n        router\n    ]); // Removed refreshStatus from dependencies to prevent infinite loop\n    // Separate effect to handle showing modal when subscription status becomes available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SuccessPageContent.useEffect\": ()=>{\n            if (!isLoading && !showModal && (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) && !autoSignIn) {\n                const currentPlan = searchParams.get('plan') || subscriptionStatus.tier;\n                if (currentPlan) {\n                    const planNames = {\n                        'starter': 'Starter',\n                        'professional': 'Professional',\n                        'enterprise': 'Enterprise'\n                    };\n                    setPlanName(planNames[currentPlan] || currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1));\n                    setShowModal(true);\n                }\n            }\n        }\n    }[\"SuccessPageContent.useEffect\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        isLoading,\n        showModal,\n        autoSignIn,\n        searchParams\n    ]);\n    const getPlanFeatures = (planType)=>{\n        switch(planType){\n            case 'starter':\n                return [\n                    'All routing strategies',\n                    'Up to 5 configurations',\n                    'Up to 15 API keys per config',\n                    'Custom roles (up to 3)',\n                    '50 user-generated API keys',\n                    '15 browsing tasks per month'\n                ];\n            case 'professional':\n                return [\n                    'Everything in Starter',\n                    'Unlimited configurations',\n                    'Unlimited API keys',\n                    'Unlimited custom roles',\n                    'Knowledge base (5 documents)',\n                    'Priority support'\n                ];\n            case 'enterprise':\n                return [\n                    'Everything in Professional',\n                    'Unlimited knowledge base documents',\n                    'Advanced semantic caching',\n                    'Custom integrations',\n                    'Dedicated support + phone',\n                    'SLA guarantee'\n                ];\n            default:\n                return [\n                    'All premium features unlocked'\n                ];\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowModal(false);\n        setTimeout(()=>{\n            router.push('/dashboard');\n        }, 300);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Processing your upgrade...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this);\n    }\n    // If manual sign-in is required, show sign-in instructions\n    if (manualSignIn) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Payment Successful! \\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Your \",\n                            planName,\n                            \" subscription has been activated. Please sign in to access your dashboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/auth/signin'),\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Sign In to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n        children: showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    scale: 0.8,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                transition: {\n                    type: \"spring\",\n                    duration: 0.5\n                },\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-amber-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 0.2,\n                            type: \"spring\",\n                            duration: 0.6\n                        },\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: [\n                            \"\\uD83C\\uDF89 Welcome to \",\n                            planName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Thank you for subscribing! You now have access to all the powerful features of the \",\n                            planName,\n                            \" plan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"What's unlocked:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-gray-700 space-y-1\",\n                                children: getPlanFeatures(plan || (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'starter').map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.li, {\n                                        initial: {\n                                            x: -20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            x: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.6 + index * 0.1\n                                        },\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1.5 h-1.5 bg-orange-500 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this),\n                                            feature\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        onClick: handleCloseModal,\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Start Building Amazing Things! \\uD83D\\uDE80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 1\n                        },\n                        className: \"text-xs text-gray-500 mt-4\",\n                        children: \"You can close this popup anytime to continue to your dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 230,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s(SuccessPageContent, \"4QD3sOmX4CpoWyr7R/fKunEXgGU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c = SuccessPageContent;\nfunction SuccessPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading success page...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SuccessPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SuccessPageContent\");\n$RefreshReg$(_c1, \"SuccessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/success/page.tsx\n"));

/***/ })

});