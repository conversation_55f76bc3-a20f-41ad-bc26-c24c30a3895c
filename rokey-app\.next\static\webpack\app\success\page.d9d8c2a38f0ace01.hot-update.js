"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/success/page",{

/***/ "(app-pages-browser)/./src/app/success/page.tsx":
/*!**********************************!*\
  !*** ./src/app/success/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SuccessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SuccessPageContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { subscriptionStatus, refreshStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [planName, setPlanName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const confettiTriggered = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const sessionId = searchParams.get('session_id');\n    const plan = searchParams.get('plan') || (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier);\n    const manualSignIn = searchParams.get('manual_signin') === 'true';\n    const autoSignIn = searchParams.get('auto_signin') === 'true';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SuccessPageContent.useEffect\": ()=>{\n            const handleAutoSignIn = {\n                \"SuccessPageContent.useEffect.handleAutoSignIn\": async ()=>{\n                    // Handle auto sign-in for pending users\n                    if (autoSignIn && userId && email) {\n                        console.log('Auto sign-in requested for user:', userId, email);\n                        try {\n                            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createSupabaseBrowserClient)();\n                            // Check if user is already signed in\n                            const { data: { user } } = await supabase.auth.getUser();\n                            if (user && user.id === userId) {\n                                console.log('User already signed in, proceeding to dashboard');\n                                router.push('/dashboard?payment_success=true');\n                                return;\n                            }\n                            // Generate a magic link for auto sign-in\n                            console.log('Generating magic link for auto sign-in...');\n                            const response = await fetch('/api/auth/generate-magic-link', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    email: email,\n                                    userId: userId,\n                                    redirectTo: '/dashboard?payment_success=true'\n                                })\n                            });\n                            if (response.ok) {\n                                const data = await response.json();\n                                if (data.magicLink) {\n                                    console.log('Magic link generated, redirecting...');\n                                    window.location.href = data.magicLink;\n                                    return;\n                                }\n                            }\n                            console.log('Magic link generation failed, falling back to manual sign-in');\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        } catch (error) {\n                            console.error('Auto sign-in error:', error);\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        }\n                    }\n                    // Normal success page flow\n                    if (confettiTriggered.current) return;\n                    // Mark confetti as triggered to prevent infinite loop\n                    confettiTriggered.current = true;\n                    // Trigger confetti animation\n                    const triggerConfetti = {\n                        \"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\": ()=>{\n                            (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                                particleCount: 100,\n                                spread: 70,\n                                origin: {\n                                    y: 0.6\n                                }\n                            });\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\"];\n                    // Initial confetti\n                    setTimeout(triggerConfetti, 500);\n                    // Additional confetti bursts\n                    setTimeout(triggerConfetti, 1500);\n                    setTimeout(triggerConfetti, 2500);\n                    // Refresh subscription status\n                    refreshStatus();\n                    // Set plan name\n                    const planNames = {\n                        'starter': 'Starter',\n                        'professional': 'Professional',\n                        'enterprise': 'Enterprise'\n                    };\n                    setPlanName(planNames[plan] || 'Professional');\n                    // Show modal after a brief delay\n                    setTimeout({\n                        \"SuccessPageContent.useEffect.handleAutoSignIn\": ()=>{\n                            setIsLoading(false);\n                            setShowModal(true);\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn\"], 1000);\n                }\n            }[\"SuccessPageContent.useEffect.handleAutoSignIn\"];\n            handleAutoSignIn();\n        }\n    }[\"SuccessPageContent.useEffect\"], [\n        plan,\n        autoSignIn,\n        userId,\n        email,\n        router\n    ]); // Removed refreshStatus from dependencies to prevent infinite loop\n    const getPlanFeatures = (planType)=>{\n        switch(planType){\n            case 'starter':\n                return [\n                    'All routing strategies',\n                    'Up to 5 configurations',\n                    'Up to 15 API keys per config',\n                    'Custom roles (up to 3)',\n                    '50 user-generated API keys',\n                    '15 browsing tasks per month'\n                ];\n            case 'professional':\n                return [\n                    'Everything in Starter',\n                    'Unlimited configurations',\n                    'Unlimited API keys',\n                    'Unlimited custom roles',\n                    'Knowledge base (5 documents)',\n                    'Priority support'\n                ];\n            case 'enterprise':\n                return [\n                    'Everything in Professional',\n                    'Unlimited knowledge base documents',\n                    'Advanced semantic caching',\n                    'Custom integrations',\n                    'Dedicated support + phone',\n                    'SLA guarantee'\n                ];\n            default:\n                return [\n                    'All premium features unlocked'\n                ];\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowModal(false);\n        setTimeout(()=>{\n            router.push('/dashboard');\n        }, 300);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Processing your upgrade...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    // If manual sign-in is required, show sign-in instructions\n    if (manualSignIn) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Payment Successful! \\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Your \",\n                            planName,\n                            \" subscription has been activated. Please sign in to access your dashboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/auth/signin'),\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Sign In to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n        children: showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    scale: 0.8,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                transition: {\n                    type: \"spring\",\n                    duration: 0.5\n                },\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-amber-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 0.2,\n                            type: \"spring\",\n                            duration: 0.6\n                        },\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: [\n                            \"\\uD83C\\uDF89 Welcome to \",\n                            planName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Thank you for subscribing! You now have access to all the powerful features of the \",\n                            planName,\n                            \" plan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"What's unlocked:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-gray-700 space-y-1\",\n                                children: getPlanFeatures(plan).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.li, {\n                                        initial: {\n                                            x: -20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            x: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.6 + index * 0.1\n                                        },\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1.5 h-1.5 bg-orange-500 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this),\n                                            feature\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        onClick: handleCloseModal,\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Start Building Amazing Things! \\uD83D\\uDE80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 1\n                        },\n                        className: \"text-xs text-gray-500 mt-4\",\n                        children: \"You can close this popup anytime to continue to your dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(SuccessPageContent, \"BbbHfeJKhwpBuJFfkQ1ecu/iqxg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c = SuccessPageContent;\nfunction SuccessPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading success page...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 318,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 310,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SuccessPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SuccessPageContent\");\n$RefreshReg$(_c1, \"SuccessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/success/page.tsx\n"));

/***/ })

});