/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/paid-signup/route";
exports.ids = ["app/api/auth/paid-signup/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpaid-signup%2Froute&page=%2Fapi%2Fauth%2Fpaid-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpaid-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpaid-signup%2Froute&page=%2Fapi%2Fauth%2Fpaid-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpaid-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_paid_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/paid-signup/route.ts */ \"(rsc)/./src/app/api/auth/paid-signup/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/paid-signup/route\",\n        pathname: \"/api/auth/paid-signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/paid-signup/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\paid-signup\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_paid_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpaid-signup%2Froute&page=%2Fapi%2Fauth%2Fpaid-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpaid-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/paid-signup/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/auth/paid-signup/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/service-role */ \"(rsc)/./src/lib/supabase/service-role.ts\");\n\n\nasync function POST(req) {\n    try {\n        const { email, password, firstName, lastName, plan } = await req.json();\n        if (!email || !password || !firstName || !lastName || !plan) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'All fields are required'\n            }, {\n                status: 400\n            });\n        }\n        if (![\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(plan)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid plan selected'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__.createServiceRoleClient)();\n        console.log('Creating paid user account for:', email, 'plan:', plan);\n        // Create user account with admin privileges (don't sign them in)\n        const { data: authData, error: authError } = await supabase.auth.admin.createUser({\n            email,\n            password,\n            email_confirm: true,\n            user_metadata: {\n                full_name: `${firstName} ${lastName}`,\n                first_name: firstName,\n                last_name: lastName,\n                plan: plan,\n                payment_status: 'pending'\n            }\n        });\n        console.log('User creation result:', {\n            success: !!authData.user,\n            userId: authData.user?.id,\n            error: authError?.message\n        });\n        if (authError) {\n            console.error('Error creating user:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: authError.message\n            }, {\n                status: 400\n            });\n        }\n        if (!authData.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create user'\n            }, {\n                status: 500\n            });\n        }\n        console.log('Creating user profile with pending status for user:', authData.user.id);\n        // Create user profile with pending status\n        const { error: profileError } = await supabase.from('user_profiles').insert({\n            id: authData.user.id,\n            full_name: `${firstName} ${lastName}`,\n            subscription_tier: plan,\n            subscription_status: 'inactive',\n            user_status: 'pending',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        });\n        console.log('Profile creation result:', {\n            success: !profileError,\n            error: profileError?.message\n        });\n        if (profileError) {\n            console.error('Error creating user profile:', profileError);\n            // Try to clean up the auth user if profile creation fails\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create user profile'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                id: authData.user.id,\n                email: authData.user.email,\n                plan: plan,\n                status: 'pending'\n            },\n            message: 'Account created successfully. Please complete payment to activate your account.'\n        });\n    } catch (error) {\n        console.error('Error in paid signup:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for testing\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Paid signup endpoint',\n        usage: 'POST /api/auth/paid-signup with { email, password, firstName, lastName, plan }'\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3BhaWQtc2lnbnVwL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDYztBQUUvRCxlQUFlRSxLQUFLQyxHQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLElBQUksRUFBRSxHQUFHLE1BQU1MLElBQUlNLElBQUk7UUFFckUsSUFBSSxDQUFDTCxTQUFTLENBQUNDLFlBQVksQ0FBQ0MsYUFBYSxDQUFDQyxZQUFZLENBQUNDLE1BQU07WUFDM0QsT0FBT1IscURBQVlBLENBQUNTLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBMEIsR0FDbkM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLElBQUksQ0FBQztZQUFDO1lBQVc7WUFBZ0I7U0FBYSxDQUFDQyxRQUFRLENBQUNKLE9BQU87WUFDN0QsT0FBT1IscURBQVlBLENBQUNTLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBd0IsR0FDakM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1FLFdBQVdaLG1GQUF1QkE7UUFFeENhLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNYLE9BQU8sU0FBU0k7UUFFL0QsaUVBQWlFO1FBQ2pFLE1BQU0sRUFBRVEsTUFBTUMsUUFBUSxFQUFFUCxPQUFPUSxTQUFTLEVBQUUsR0FBRyxNQUFNTCxTQUFTTSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO1lBQ2hGakI7WUFDQUM7WUFDQWlCLGVBQWU7WUFDZkMsZUFBZTtnQkFDYkMsV0FBVyxHQUFHbEIsVUFBVSxDQUFDLEVBQUVDLFVBQVU7Z0JBQ3JDa0IsWUFBWW5CO2dCQUNab0IsV0FBV25CO2dCQUNYQyxNQUFNQTtnQkFDTm1CLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUFiLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUI7WUFDbkNhLFNBQVMsQ0FBQyxDQUFDWCxTQUFTWSxJQUFJO1lBQ3hCQyxRQUFRYixTQUFTWSxJQUFJLEVBQUVFO1lBQ3ZCckIsT0FBT1EsV0FBV2M7UUFDcEI7UUFFQSxJQUFJZCxXQUFXO1lBQ2JKLFFBQVFKLEtBQUssQ0FBQyx3QkFBd0JRO1lBQ3RDLE9BQU9sQixxREFBWUEsQ0FBQ1MsSUFBSSxDQUN0QjtnQkFBRUMsT0FBT1EsVUFBVWMsT0FBTztZQUFDLEdBQzNCO2dCQUFFckIsUUFBUTtZQUFJO1FBRWxCO1FBRUEsSUFBSSxDQUFDTSxTQUFTWSxJQUFJLEVBQUU7WUFDbEIsT0FBTzdCLHFEQUFZQSxDQUFDUyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXdCLEdBQ2pDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQUcsUUFBUUMsR0FBRyxDQUFDLHVEQUF1REUsU0FBU1ksSUFBSSxDQUFDRSxFQUFFO1FBRW5GLDBDQUEwQztRQUMxQyxNQUFNLEVBQUVyQixPQUFPdUIsWUFBWSxFQUFFLEdBQUcsTUFBTXBCLFNBQ25DcUIsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUM7WUFDTkosSUFBSWQsU0FBU1ksSUFBSSxDQUFDRSxFQUFFO1lBQ3BCUCxXQUFXLEdBQUdsQixVQUFVLENBQUMsRUFBRUMsVUFBVTtZQUNyQzZCLG1CQUFtQjVCO1lBQ25CNkIscUJBQXFCO1lBQ3JCQyxhQUFhO1lBQ2JDLFlBQVksSUFBSUMsT0FBT0MsV0FBVztZQUNsQ0MsWUFBWSxJQUFJRixPQUFPQyxXQUFXO1FBQ3BDO1FBRUYzQixRQUFRQyxHQUFHLENBQUMsNEJBQTRCO1lBQ3RDYSxTQUFTLENBQUNLO1lBQ1Z2QixPQUFPdUIsY0FBY0Q7UUFDdkI7UUFFQSxJQUFJQyxjQUFjO1lBQ2hCbkIsUUFBUUosS0FBSyxDQUFDLGdDQUFnQ3VCO1lBQzlDLDBEQUEwRDtZQUMxRCxNQUFNcEIsU0FBU00sSUFBSSxDQUFDQyxLQUFLLENBQUN1QixVQUFVLENBQUMxQixTQUFTWSxJQUFJLENBQUNFLEVBQUU7WUFDckQsT0FBTy9CLHFEQUFZQSxDQUFDUyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQWdDLEdBQ3pDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxPQUFPWCxxREFBWUEsQ0FBQ1MsSUFBSSxDQUFDO1lBQ3ZCbUIsU0FBUztZQUNUQyxNQUFNO2dCQUNKRSxJQUFJZCxTQUFTWSxJQUFJLENBQUNFLEVBQUU7Z0JBQ3BCM0IsT0FBT2EsU0FBU1ksSUFBSSxDQUFDekIsS0FBSztnQkFDMUJJLE1BQU1BO2dCQUNORyxRQUFRO1lBQ1Y7WUFDQXFCLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT3RCLE9BQU87UUFDZEksUUFBUUosS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT1YscURBQVlBLENBQUNTLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUF3QixHQUNqQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDJCQUEyQjtBQUNwQixlQUFlaUM7SUFDcEIsT0FBTzVDLHFEQUFZQSxDQUFDUyxJQUFJLENBQUM7UUFDdkJ1QixTQUFTO1FBQ1RhLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxhcGlcXGF1dGhcXHBhaWQtc2lnbnVwXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlU2VydmljZVJvbGVDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zZXJ2aWNlLXJvbGUnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBlbWFpbCwgcGFzc3dvcmQsIGZpcnN0TmFtZSwgbGFzdE5hbWUsIHBsYW4gfSA9IGF3YWl0IHJlcS5qc29uKCk7XG5cbiAgICBpZiAoIWVtYWlsIHx8ICFwYXNzd29yZCB8fCAhZmlyc3ROYW1lIHx8ICFsYXN0TmFtZSB8fCAhcGxhbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQWxsIGZpZWxkcyBhcmUgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoIVsnc3RhcnRlcicsICdwcm9mZXNzaW9uYWwnLCAnZW50ZXJwcmlzZSddLmluY2x1ZGVzKHBsYW4pKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdJbnZhbGlkIHBsYW4gc2VsZWN0ZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZpY2VSb2xlQ2xpZW50KCk7XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgcGFpZCB1c2VyIGFjY291bnQgZm9yOicsIGVtYWlsLCAncGxhbjonLCBwbGFuKTtcblxuICAgIC8vIENyZWF0ZSB1c2VyIGFjY291bnQgd2l0aCBhZG1pbiBwcml2aWxlZ2VzIChkb24ndCBzaWduIHRoZW0gaW4pXG4gICAgY29uc3QgeyBkYXRhOiBhdXRoRGF0YSwgZXJyb3I6IGF1dGhFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5hZG1pbi5jcmVhdGVVc2VyKHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgICBlbWFpbF9jb25maXJtOiB0cnVlLCAvLyBBdXRvLWNvbmZpcm0gZW1haWxcbiAgICAgIHVzZXJfbWV0YWRhdGE6IHtcbiAgICAgICAgZnVsbF9uYW1lOiBgJHtmaXJzdE5hbWV9ICR7bGFzdE5hbWV9YCxcbiAgICAgICAgZmlyc3RfbmFtZTogZmlyc3ROYW1lLFxuICAgICAgICBsYXN0X25hbWU6IGxhc3ROYW1lLFxuICAgICAgICBwbGFuOiBwbGFuLFxuICAgICAgICBwYXltZW50X3N0YXR1czogJ3BlbmRpbmcnXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coJ1VzZXIgY3JlYXRpb24gcmVzdWx0OicsIHtcbiAgICAgIHN1Y2Nlc3M6ICEhYXV0aERhdGEudXNlcixcbiAgICAgIHVzZXJJZDogYXV0aERhdGEudXNlcj8uaWQsXG4gICAgICBlcnJvcjogYXV0aEVycm9yPy5tZXNzYWdlXG4gICAgfSk7XG5cbiAgICBpZiAoYXV0aEVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyOicsIGF1dGhFcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6IGF1dGhFcnJvci5tZXNzYWdlIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoIWF1dGhEYXRhLnVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgdXNlcicgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdDcmVhdGluZyB1c2VyIHByb2ZpbGUgd2l0aCBwZW5kaW5nIHN0YXR1cyBmb3IgdXNlcjonLCBhdXRoRGF0YS51c2VyLmlkKTtcblxuICAgIC8vIENyZWF0ZSB1c2VyIHByb2ZpbGUgd2l0aCBwZW5kaW5nIHN0YXR1c1xuICAgIGNvbnN0IHsgZXJyb3I6IHByb2ZpbGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICBpZDogYXV0aERhdGEudXNlci5pZCxcbiAgICAgICAgZnVsbF9uYW1lOiBgJHtmaXJzdE5hbWV9ICR7bGFzdE5hbWV9YCxcbiAgICAgICAgc3Vic2NyaXB0aW9uX3RpZXI6IHBsYW4sXG4gICAgICAgIHN1YnNjcmlwdGlvbl9zdGF0dXM6ICdpbmFjdGl2ZScsIC8vIFdpbGwgYmUgYWN0aXZhdGVkIGFmdGVyIHBheW1lbnRcbiAgICAgICAgdXNlcl9zdGF0dXM6ICdwZW5kaW5nJywgLy8gQ2Fubm90IHNpZ24gaW4gdW50aWwgcGF5bWVudCBpcyBjb21wbGV0ZVxuICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgY3JlYXRpb24gcmVzdWx0OicsIHtcbiAgICAgIHN1Y2Nlc3M6ICFwcm9maWxlRXJyb3IsXG4gICAgICBlcnJvcjogcHJvZmlsZUVycm9yPy5tZXNzYWdlXG4gICAgfSk7XG5cbiAgICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyIHByb2ZpbGU6JywgcHJvZmlsZUVycm9yKTtcbiAgICAgIC8vIFRyeSB0byBjbGVhbiB1cCB0aGUgYXV0aCB1c2VyIGlmIHByb2ZpbGUgY3JlYXRpb24gZmFpbHNcbiAgICAgIGF3YWl0IHN1cGFiYXNlLmF1dGguYWRtaW4uZGVsZXRlVXNlcihhdXRoRGF0YS51c2VyLmlkKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgdXNlciBwcm9maWxlJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB1c2VyOiB7XG4gICAgICAgIGlkOiBhdXRoRGF0YS51c2VyLmlkLFxuICAgICAgICBlbWFpbDogYXV0aERhdGEudXNlci5lbWFpbCxcbiAgICAgICAgcGxhbjogcGxhbixcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZydcbiAgICAgIH0sXG4gICAgICBtZXNzYWdlOiAnQWNjb3VudCBjcmVhdGVkIHN1Y2Nlc3NmdWxseS4gUGxlYXNlIGNvbXBsZXRlIHBheW1lbnQgdG8gYWN0aXZhdGUgeW91ciBhY2NvdW50LicsXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBwYWlkIHNpZ251cDonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gR0VUIGVuZHBvaW50IGZvciB0ZXN0aW5nXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgIG1lc3NhZ2U6ICdQYWlkIHNpZ251cCBlbmRwb2ludCcsXG4gICAgdXNhZ2U6ICdQT1NUIC9hcGkvYXV0aC9wYWlkLXNpZ251cCB3aXRoIHsgZW1haWwsIHBhc3N3b3JkLCBmaXJzdE5hbWUsIGxhc3ROYW1lLCBwbGFuIH0nXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZVNlcnZpY2VSb2xlQ2xpZW50IiwiUE9TVCIsInJlcSIsImVtYWlsIiwicGFzc3dvcmQiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInBsYW4iLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJpbmNsdWRlcyIsInN1cGFiYXNlIiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJhdXRoRGF0YSIsImF1dGhFcnJvciIsImF1dGgiLCJhZG1pbiIsImNyZWF0ZVVzZXIiLCJlbWFpbF9jb25maXJtIiwidXNlcl9tZXRhZGF0YSIsImZ1bGxfbmFtZSIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJwYXltZW50X3N0YXR1cyIsInN1Y2Nlc3MiLCJ1c2VyIiwidXNlcklkIiwiaWQiLCJtZXNzYWdlIiwicHJvZmlsZUVycm9yIiwiZnJvbSIsImluc2VydCIsInN1YnNjcmlwdGlvbl90aWVyIiwic3Vic2NyaXB0aW9uX3N0YXR1cyIsInVzZXJfc3RhdHVzIiwiY3JlYXRlZF9hdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRfYXQiLCJkZWxldGVVc2VyIiwiR0VUIiwidXNhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/paid-signup/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/service-role.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/service-role.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Create service role client for server-side operations that need to bypass RLS\n * This client has full database access and should only be used for:\n * - Internal API operations\n * - Admin operations\n * - System-level operations\n * - External API key authentication\n */ function createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZpY2Utcm9sZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUVyRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0M7SUFDZCxPQUFPRCxzR0FBWUEsQ0FDakJFLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUIsRUFDckM7UUFDRUMsTUFBTTtZQUNKQyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2aWNlLXJvbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLyoqXG4gKiBDcmVhdGUgc2VydmljZSByb2xlIGNsaWVudCBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9ucyB0aGF0IG5lZWQgdG8gYnlwYXNzIFJMU1xuICogVGhpcyBjbGllbnQgaGFzIGZ1bGwgZGF0YWJhc2UgYWNjZXNzIGFuZCBzaG91bGQgb25seSBiZSB1c2VkIGZvcjpcbiAqIC0gSW50ZXJuYWwgQVBJIG9wZXJhdGlvbnNcbiAqIC0gQWRtaW4gb3BlcmF0aW9uc1xuICogLSBTeXN0ZW0tbGV2ZWwgb3BlcmF0aW9uc1xuICogLSBFeHRlcm5hbCBBUEkga2V5IGF1dGhlbnRpY2F0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/service-role.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpaid-signup%2Froute&page=%2Fapi%2Fauth%2Fpaid-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpaid-signup%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();