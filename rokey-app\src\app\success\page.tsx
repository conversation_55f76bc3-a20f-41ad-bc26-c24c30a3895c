'use client';

import { useEffect, useState, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';
import { CheckCircleIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import confetti from 'canvas-confetti';
import Image from 'next/image';
import Link from 'next/link';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

function SuccessPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { subscriptionStatus, refreshStatus } = useSubscription();
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const confettiTriggered = useRef(false);

  const sessionId = searchParams.get('session_id');
  const manualSignIn = searchParams.get('manual_signin') === 'true';
  const autoSignIn = searchParams.get('auto_signin') === 'true';
  const userId = searchParams.get('user_id');
  const email = searchParams.get('email');

  useEffect(() => {
    const handleAutoSignIn = async () => {
      // Handle auto sign-in for pending users
      if (autoSignIn && userId && email) {
        console.log('Auto sign-in requested for user:', userId, email);

        try {
          const supabase = createSupabaseBrowserClient();

          // Check if user is already signed in
          const { data: { user } } = await supabase.auth.getUser();

          if (user && user.id === userId) {
            console.log('User already signed in, proceeding to dashboard');
            router.push('/dashboard?payment_success=true');
            return;
          }

          // Generate a magic link for auto sign-in
          console.log('Generating magic link for auto sign-in...');
          const response = await fetch('/api/auth/generate-magic-link', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: email,
              userId: userId,
              redirectTo: '/dashboard?payment_success=true'
            }),
          });

          if (response.ok) {
            const data = await response.json();
            if (data.magicLink) {
              console.log('Magic link generated, redirecting...');
              window.location.href = data.magicLink;
              return;
            }
          }

          console.log('Magic link generation failed, falling back to manual sign-in');
          // Fallback to manual sign-in
          router.push(`/auth/signin?email=${encodeURIComponent(email)}&message=payment_success`);
          return;

        } catch (error) {
          console.error('Auto sign-in error:', error);
          // Fallback to manual sign-in
          router.push(`/auth/signin?email=${encodeURIComponent(email)}&message=payment_success`);
          return;
        }
      }

      // Normal success page flow
      if (confettiTriggered.current) return;

      // Mark confetti as triggered to prevent infinite loop
      confettiTriggered.current = true;

      // Trigger confetti animation
      const triggerConfetti = () => {
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 }
        });
      };

      // Initial confetti
      setTimeout(triggerConfetti, 500);

      // Additional confetti bursts
      setTimeout(triggerConfetti, 1500);
      setTimeout(triggerConfetti, 2500);

      // Refresh subscription status
      refreshStatus();

      // Show modal after a brief delay
      setTimeout(() => {
        setIsLoading(false);
        setShowModal(true);
      }, 1000);
    };

    handleAutoSignIn();
  }, [autoSignIn, userId, email, router]); // Removed refreshStatus from dependencies to prevent infinite loop

  const handleCloseModal = () => {
    setShowModal(false);
    setTimeout(() => {
      router.push('/dashboard');
    }, 300);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Processing your upgrade...</p>
        </div>
      </div>
    );
  }

  // If manual sign-in is required, show sign-in instructions
  if (manualSignIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircleIcon className="w-10 h-10 text-white" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Payment Successful! 🎉
          </h1>

          <p className="text-gray-600 mb-6">
            Your {planName} subscription has been activated. Please sign in to access your dashboard.
          </p>

          <button
            onClick={() => router.push('/auth/signin')}
            className="w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Sign In to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4">
      {/* Success Modal */}
      {showModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center relative overflow-hidden"
          >
            {/* Background decoration */}
            <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-amber-500"></div>
            
            {/* Success icon with animation */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", duration: 0.6 }}
              className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <CheckCircleIcon className="w-10 h-10 text-white" />
            </motion.div>

            {/* Title */}
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              🎉 Thank you for choosing RouKey!
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              You're all set! Start building amazing things with RouKey's powerful AI routing platform.
            </motion.p>

            {/* Features list */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-4 mb-6"
            >
              <div className="flex items-center justify-center mb-3">
                <SparklesIcon className="w-5 h-5 text-orange-500 mr-2" />
                <span className="font-semibold text-gray-900">Ready to explore:</span>
              </div>
              <ul className="text-sm text-gray-700 space-y-1">
                {[
                  'Smart AI routing strategies',
                  'Multiple model configurations',
                  'Custom role-based routing',
                  'Real-time analytics dashboard',
                  'API key management',
                  'Advanced request monitoring'
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    className="flex items-center"
                  >
                    <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
                    {feature}
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* CTA Button */}
            <motion.button
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.8 }}
              onClick={handleCloseModal}
              className="w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Start Building Amazing Things! 🚀
            </motion.button>

            {/* Small note */}
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="text-xs text-gray-500 mt-4"
            >
              You can close this popup anytime to continue to your dashboard
            </motion.p>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}

export default function SuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading success page...</p>
        </div>
      </div>
    }>
      <SuccessPageContent />
    </Suspense>
  );
}
