/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/payment-success/route";
exports.ids = ["app/api/stripe/payment-success/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_payment_success_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/payment-success/route.ts */ \"(rsc)/./src/app/api/stripe/payment-success/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/payment-success/route\",\n        pathname: \"/api/stripe/payment-success\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/payment-success/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\payment-success\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_payment_success_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/payment-success/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/stripe/payment-success/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/service-role */ \"(rsc)/./src/lib/supabase/service-role.ts\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_2__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2024-06-20'\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const sessionId = searchParams.get('session_id');\n        const plan = searchParams.get('plan');\n        if (!sessionId) {\n            console.error('No session ID provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=no_session', req.url));\n        }\n        console.log('Processing payment success for session:', sessionId);\n        // Retrieve the checkout session from Stripe\n        const session = await stripe.checkout.sessions.retrieve(sessionId);\n        if (!session) {\n            console.error('Session not found:', sessionId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=session_not_found', req.url));\n        }\n        // Get user ID from session metadata\n        const userId = session.metadata?.user_id;\n        if (!userId || userId === 'pending_signup') {\n            console.error('No user ID in session metadata');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=no_user_id', req.url));\n        }\n        console.log('Found user ID in session:', userId);\n        const supabase = (0,_lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__.createServiceRoleClient)();\n        // Check if user is now active (webhook should have processed by now)\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('user_status, subscription_status, subscription_tier').eq('id', userId).single();\n        if (profileError) {\n            console.error('Error fetching user profile:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=profile_error', req.url));\n        }\n        if (!profile) {\n            console.error('User profile not found for user:', userId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=profile_not_found', req.url));\n        }\n        console.log('User profile status:', profile);\n        // If user is not yet active, wait a moment for webhook processing\n        if (profile.user_status !== 'active') {\n            console.log('User not yet active, checking again in a moment...');\n            // Wait 2 seconds and check again\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const { data: updatedProfile } = await supabase.from('user_profiles').select('user_status, subscription_status, subscription_tier').eq('id', userId).single();\n            if (updatedProfile?.user_status !== 'active') {\n                console.log('User still not active after wait, but payment succeeded - activating manually');\n                // Manually activate the user (webhook might be delayed)\n                const { error: updateError } = await supabase.from('user_profiles').update({\n                    user_status: 'active',\n                    subscription_status: 'active',\n                    updated_at: new Date().toISOString()\n                }).eq('id', userId);\n                if (updateError) {\n                    console.error('Error manually activating user:', updateError);\n                } else {\n                    console.log('User manually activated after payment success');\n                }\n            }\n        }\n        // Get user details for auto sign-in\n        const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);\n        if (userError || !userData.user) {\n            console.error('Error fetching user for session creation:', userError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));\n        }\n        console.log('User found, redirecting to success page with auto sign-in');\n        // Redirect to success page with user details for auto sign-in\n        const successUrl = new URL('/success', req.url);\n        successUrl.searchParams.set('session_id', sessionId);\n        successUrl.searchParams.set('plan', plan || '');\n        successUrl.searchParams.set('user_id', userId);\n        successUrl.searchParams.set('email', userData.user.email);\n        successUrl.searchParams.set('auto_signin', 'true');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(successUrl);\n    } catch (error) {\n        console.error('Error in payment success handler:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=payment_success_error', req.url));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/payment-success/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/service-role.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/service-role.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Create service role client for server-side operations that need to bypass RLS\n * This client has full database access and should only be used for:\n * - Internal API operations\n * - Admin operations\n * - System-level operations\n * - External API key authentication\n */ function createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZpY2Utcm9sZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUVyRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0M7SUFDZCxPQUFPRCxzR0FBWUEsQ0FDakJFLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUIsRUFDckM7UUFDRUMsTUFBTTtZQUNKQyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2aWNlLXJvbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLyoqXG4gKiBDcmVhdGUgc2VydmljZSByb2xlIGNsaWVudCBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9ucyB0aGF0IG5lZWQgdG8gYnlwYXNzIFJMU1xuICogVGhpcyBjbGllbnQgaGFzIGZ1bGwgZGF0YWJhc2UgYWNjZXNzIGFuZCBzaG91bGQgb25seSBiZSB1c2VkIGZvcjpcbiAqIC0gSW50ZXJuYWwgQVBJIG9wZXJhdGlvbnNcbiAqIC0gQWRtaW4gb3BlcmF0aW9uc1xuICogLSBTeXN0ZW0tbGV2ZWwgb3BlcmF0aW9uc1xuICogLSBFeHRlcm5hbCBBUEkga2V5IGF1dGhlbnRpY2F0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/service-role.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();