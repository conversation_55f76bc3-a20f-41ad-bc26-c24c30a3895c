"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/success/page",{

/***/ "(app-pages-browser)/./src/app/success/page.tsx":
/*!**********************************!*\
  !*** ./src/app/success/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SuccessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SuccessPageContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { subscriptionStatus, refreshStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [planName, setPlanName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const confettiTriggered = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const sessionId = searchParams.get('session_id');\n    const plan = searchParams.get('plan') || (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier);\n    const manualSignIn = searchParams.get('manual_signin') === 'true';\n    const autoSignIn = searchParams.get('auto_signin') === 'true';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SuccessPageContent.useEffect\": ()=>{\n            const handleAutoSignIn = {\n                \"SuccessPageContent.useEffect.handleAutoSignIn\": async ()=>{\n                    // Handle auto sign-in for pending users\n                    if (autoSignIn && userId && email) {\n                        console.log('Auto sign-in requested for user:', userId, email);\n                        try {\n                            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createSupabaseBrowserClient)();\n                            // Check if user is already signed in\n                            const { data: { user } } = await supabase.auth.getUser();\n                            if (user && user.id === userId) {\n                                console.log('User already signed in, proceeding to dashboard');\n                                router.push('/dashboard?payment_success=true');\n                                return;\n                            }\n                            // Generate a magic link for auto sign-in\n                            console.log('Generating magic link for auto sign-in...');\n                            const response = await fetch('/api/auth/generate-magic-link', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    email: email,\n                                    userId: userId,\n                                    redirectTo: '/dashboard?payment_success=true'\n                                })\n                            });\n                            if (response.ok) {\n                                const data = await response.json();\n                                if (data.magicLink) {\n                                    console.log('Magic link generated, redirecting...');\n                                    window.location.href = data.magicLink;\n                                    return;\n                                }\n                            }\n                            console.log('Magic link generation failed, falling back to manual sign-in');\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        } catch (error) {\n                            console.error('Auto sign-in error:', error);\n                            // Fallback to manual sign-in\n                            router.push(\"/auth/signin?email=\".concat(encodeURIComponent(email), \"&message=payment_success\"));\n                            return;\n                        }\n                    }\n                    // Normal success page flow\n                    if (confettiTriggered.current) return;\n                    // Mark confetti as triggered to prevent infinite loop\n                    confettiTriggered.current = true;\n                    // Trigger confetti animation\n                    const triggerConfetti = {\n                        \"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\": ()=>{\n                            (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                                particleCount: 100,\n                                spread: 70,\n                                origin: {\n                                    y: 0.6\n                                }\n                            });\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn.triggerConfetti\"];\n                    // Initial confetti\n                    setTimeout(triggerConfetti, 500);\n                    // Additional confetti bursts\n                    setTimeout(triggerConfetti, 1500);\n                    setTimeout(triggerConfetti, 2500);\n                    // Refresh subscription status\n                    refreshStatus();\n                    // Set plan name - only if we have a valid plan\n                    if (plan) {\n                        const planNames = {\n                            'starter': 'Starter',\n                            'professional': 'Professional',\n                            'enterprise': 'Enterprise'\n                        };\n                        setPlanName(planNames[plan] || plan.charAt(0).toUpperCase() + plan.slice(1));\n                    }\n                    // Show modal after a brief delay, but only if we have plan information\n                    setTimeout({\n                        \"SuccessPageContent.useEffect.handleAutoSignIn\": ()=>{\n                            setIsLoading(false);\n                            if (plan) {\n                                setShowModal(true);\n                            }\n                        }\n                    }[\"SuccessPageContent.useEffect.handleAutoSignIn\"], 1000);\n                }\n            }[\"SuccessPageContent.useEffect.handleAutoSignIn\"];\n            handleAutoSignIn();\n        }\n    }[\"SuccessPageContent.useEffect\"], [\n        plan,\n        autoSignIn,\n        userId,\n        email,\n        router\n    ]); // Removed refreshStatus from dependencies to prevent infinite loop\n    const getPlanFeatures = (planType)=>{\n        switch(planType){\n            case 'starter':\n                return [\n                    'All routing strategies',\n                    'Up to 5 configurations',\n                    'Up to 15 API keys per config',\n                    'Custom roles (up to 3)',\n                    '50 user-generated API keys',\n                    '15 browsing tasks per month'\n                ];\n            case 'professional':\n                return [\n                    'Everything in Starter',\n                    'Unlimited configurations',\n                    'Unlimited API keys',\n                    'Unlimited custom roles',\n                    'Knowledge base (5 documents)',\n                    'Priority support'\n                ];\n            case 'enterprise':\n                return [\n                    'Everything in Professional',\n                    'Unlimited knowledge base documents',\n                    'Advanced semantic caching',\n                    'Custom integrations',\n                    'Dedicated support + phone',\n                    'SLA guarantee'\n                ];\n            default:\n                return [\n                    'All premium features unlocked'\n                ];\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowModal(false);\n        setTimeout(()=>{\n            router.push('/dashboard');\n        }, 300);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Processing your upgrade...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    // If manual sign-in is required, show sign-in instructions\n    if (manualSignIn) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Payment Successful! \\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Your \",\n                            planName,\n                            \" subscription has been activated. Please sign in to access your dashboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/auth/signin'),\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Sign In to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-4\",\n        children: showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    scale: 0.8,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                transition: {\n                    type: \"spring\",\n                    duration: 0.5\n                },\n                className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-amber-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 0.2,\n                            type: \"spring\",\n                            duration: 0.6\n                        },\n                        className: \"w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: [\n                            \"\\uD83C\\uDF89 Welcome to \",\n                            planName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Thank you for subscribing! You now have access to all the powerful features of the \",\n                            planName,\n                            \" plan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"What's unlocked:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-gray-700 space-y-1\",\n                                children: getPlanFeatures(plan).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.li, {\n                                        initial: {\n                                            x: -20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            x: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.6 + index * 0.1\n                                        },\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1.5 h-1.5 bg-orange-500 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            feature\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        onClick: handleCloseModal,\n                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                        children: \"Start Building Amazing Things! \\uD83D\\uDE80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 1\n                        },\n                        className: \"text-xs text-gray-500 mt-4\",\n                        children: \"You can close this popup anytime to continue to your dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n_s(SuccessPageContent, \"BbbHfeJKhwpBuJFfkQ1ecu/iqxg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c = SuccessPageContent;\nfunction SuccessPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading success page...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 315,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SuccessPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\success\\\\page.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SuccessPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SuccessPageContent\");\n$RefreshReg$(_c1, \"SuccessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/success/page.tsx\n"));

/***/ })

});