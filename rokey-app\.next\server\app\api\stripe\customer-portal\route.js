/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/customer-portal/route";
exports.ids = ["app/api/stripe/customer-portal/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_customer_portal_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/customer-portal/route.ts */ \"(rsc)/./src/app/api/stripe/customer-portal/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/customer-portal/route\",\n        pathname: \"/api/stripe/customer-portal\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/customer-portal/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\customer-portal\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_customer_portal_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/customer-portal/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/stripe/customer-portal/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2025-02-24.acacia'\n});\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        const { userId } = body;\n        console.log('Customer portal request for user:', userId);\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Get user's subscription record to find their Stripe customer ID\n        // We look for any subscription record (active, canceled, etc.) since we need the customer ID\n        const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions').select('stripe_customer_id').eq('user_id', userId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        let stripeCustomerId = null;\n        if (subscription && subscription.stripe_customer_id) {\n            stripeCustomerId = subscription.stripe_customer_id;\n        } else {\n            // Fallback: Try to find customer by email in Stripe\n            console.log('No subscription record found, trying to find customer by email...');\n            // Get user email from auth\n            const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);\n            if (userError || !userData.user?.email) {\n                console.error('Could not get user email:', userError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'No Stripe customer found for user'\n                }, {\n                    status: 404\n                });\n            }\n            // Search for existing Stripe customer by email\n            try {\n                const existingCustomers = await stripe.customers.list({\n                    email: userData.user.email,\n                    limit: 1\n                });\n                if (existingCustomers.data.length > 0) {\n                    stripeCustomerId = existingCustomers.data[0].id;\n                    console.log('Found existing Stripe customer by email:', stripeCustomerId);\n                } else {\n                    // Create a new Stripe customer if none exists\n                    console.log('Creating new Stripe customer for user...');\n                    const customer = await stripe.customers.create({\n                        email: userData.user.email,\n                        metadata: {\n                            supabase_user_id: userId\n                        }\n                    });\n                    stripeCustomerId = customer.id;\n                    console.log('Created new Stripe customer:', stripeCustomerId);\n                }\n            } catch (stripeError) {\n                console.error('Error handling Stripe customer:', stripeError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to create or find Stripe customer'\n                }, {\n                    status: 500\n                });\n            }\n        }\n        if (!stripeCustomerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No Stripe customer found for user'\n            }, {\n                status: 404\n            });\n        }\n        // Create customer portal session with dynamic return URL\n        const { returnUrl } = body;\n        const finalReturnUrl = returnUrl || `${ true ? 'http://localhost:3000' : 0}/billing`;\n        console.log('Creating portal session for customer:', stripeCustomerId);\n        const portalSession = await stripe.billingPortal.sessions.create({\n            customer: stripeCustomerId,\n            return_url: finalReturnUrl\n        });\n        console.log('Portal session created successfully:', portalSession.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            url: portalSession.url\n        });\n    } catch (error) {\n        console.error('Error creating customer portal session:', error);\n        if (error instanceof stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"].errors.StripeError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Stripe error: ${error.message}`\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/customer-portal/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();